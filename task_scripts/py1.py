import os

# 获取当前脚本所在目录的上一级目录
base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 源目录和目标目录
src_dir = os.path.join(base_dir, "extract-timelines", "DeepSeek-R1")
dst_dir = os.path.join(base_dir, "diversified_events")

# 创建目标文件夹（如果不存在）
os.makedirs(dst_dir, exist_ok=True)

# 遍历源目录下的所有csv文件
for filename in os.listdir(src_dir):
    if filename.endswith(".csv"):
        # 在目标目录下创建同名空文件
        dst_path = os.path.join(dst_dir, filename)
        with open(dst_path, "w") as f:
            pass  # 创建空文件
