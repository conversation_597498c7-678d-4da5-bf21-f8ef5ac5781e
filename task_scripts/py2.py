import os

# 源文件夹和目标文件夹路径
gpt4_dir = os.path.join('extract-timelines', 'GPT-4')
target_dir = 'many_to_one_events'

# 创建目标文件夹（如果不存在）
os.makedirs(target_dir, exist_ok=True)

# 遍历源文件夹下的所有文件
for filename in os.listdir(gpt4_dir):
    file_path = os.path.join(gpt4_dir, filename)
    # 只处理文件，忽略子文件夹
    if os.path.isfile(file_path):
        # 在目标文件夹下创建同名空文件
        open(os.path.join(target_dir, filename), 'w').close()
